"""
Visualization utilities for pose estimation and tracking
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.collections import LineCollection
import torch


# PoseTrack21 skeleton connections
SKELETON_CONNECTIONS = [
    [15, 13], [13, 11], [16, 14], [14, 12], [11, 12],  # legs
    [5, 11], [6, 12], [5, 6],  # torso
    [5, 7], [6, 8], [7, 9], [8, 10],  # arms
    [1, 2], [0, 1], [0, 2], [1, 3], [2, 4], [3, 5], [4, 6]  # head
]

# Colors for different body parts
COLORS = {
    'head': (255, 0, 0),      # Red
    'torso': (0, 255, 0),     # Green
    'arms': (0, 0, 255),      # Blue
    'legs': (255, 255, 0),    # Yellow
    'keypoint': (255, 255, 255)  # White
}

# Joint names for PoseTrack21
JOINT_NAMES = [
    'nose', 'head_bottom', 'head_top', 'left_ear', 'right_ear',
    'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
    'left_wrist', 'right_wrist', 'left_hip', 'right_hip',
    'left_knee', 'right_knee', 'left_ankle', 'right_ankle'
]


def draw_keypoints(image, keypoints, visibility=None, color=(255, 255, 255), radius=3):
    """
    Draw keypoints on image
    
    Args:
        image: Input image (H, W, 3)
        keypoints: Keypoints array (J, 2)
        visibility: Visibility flags (J,)
        color: Color for keypoints
        radius: Radius of keypoint circles
        
    Returns:
        Image with keypoints drawn
    """
    image = image.copy()
    
    for i, (x, y) in enumerate(keypoints):
        if visibility is None or visibility[i] > 0:
            cv2.circle(image, (int(x), int(y)), radius, color, -1)
    
    return image


def draw_skeleton(image, keypoints, visibility=None, connections=None, color=(0, 255, 0), thickness=2):
    """
    Draw skeleton connections on image
    
    Args:
        image: Input image (H, W, 3)
        keypoints: Keypoints array (J, 2)
        visibility: Visibility flags (J,)
        connections: List of joint connections
        color: Color for skeleton lines
        thickness: Line thickness
        
    Returns:
        Image with skeleton drawn
    """
    image = image.copy()
    
    if connections is None:
        connections = SKELETON_CONNECTIONS
    
    for joint1_idx, joint2_idx in connections:
        if joint1_idx < len(keypoints) and joint2_idx < len(keypoints):
            # Check if both joints are visible
            if (visibility is None or 
                (visibility[joint1_idx] > 0 and visibility[joint2_idx] > 0)):
                
                pt1 = (int(keypoints[joint1_idx][0]), int(keypoints[joint1_idx][1]))
                pt2 = (int(keypoints[joint2_idx][0]), int(keypoints[joint2_idx][1]))
                
                cv2.line(image, pt1, pt2, color, thickness)
    
    return image


def draw_pose(image, keypoints, visibility=None, track_id=None, color=None):
    """
    Draw complete pose (keypoints + skeleton) on image
    
    Args:
        image: Input image (H, W, 3)
        keypoints: Keypoints array (J, 2)
        visibility: Visibility flags (J,)
        track_id: Track ID for labeling
        color: Color for pose (if None, use default)
        
    Returns:
        Image with pose drawn
    """
    if color is None:
        # Generate color based on track_id
        if track_id is not None:
            np.random.seed(track_id)
            color = tuple(np.random.randint(0, 255, 3).tolist())
        else:
            color = (0, 255, 0)
    
    # Draw skeleton
    image = draw_skeleton(image, keypoints, visibility, color=color)
    
    # Draw keypoints
    image = draw_keypoints(image, keypoints, visibility, color=(255, 255, 255))
    
    # Draw track ID if provided
    if track_id is not None and len(keypoints) > 0:
        # Use head position for text
        head_pos = keypoints[0]  # nose
        cv2.putText(image, f'ID:{track_id}', 
                   (int(head_pos[0]), int(head_pos[1]) - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
    
    return image


def visualize_predictions(images, predictions, ground_truth=None, save_path=None):
    """
    Visualize model predictions
    
    Args:
        images: Input images (B, 3, H, W) or (B, H, W, 3)
        predictions: Model predictions
        ground_truth: Ground truth annotations (optional)
        save_path: Path to save visualization
        
    Returns:
        Visualization image
    """
    if isinstance(images, torch.Tensor):
        images = images.cpu().numpy()
    
    # Handle different image formats
    if images.ndim == 4 and images.shape[1] == 3:  # (B, 3, H, W)
        images = images.transpose(0, 2, 3, 1)  # (B, H, W, 3)
    
    # Denormalize if needed (assuming ImageNet normalization)
    if images.max() <= 1.0:
        mean = np.array([0.485, 0.456, 0.406])
        std = np.array([0.229, 0.224, 0.225])
        images = images * std + mean
        images = np.clip(images * 255, 0, 255).astype(np.uint8)
    
    batch_size = len(images)
    fig, axes = plt.subplots(2, batch_size, figsize=(4*batch_size, 8))
    
    if batch_size == 1:
        axes = axes.reshape(2, 1)
    
    for i in range(batch_size):
        image = images[i]
        
        # Top row: predictions
        pred_image = image.copy()
        if 'keypoint_coord' in predictions:
            pred_keypoints = predictions['keypoint_coord'][i].cpu().numpy()
            pred_scores = predictions.get('keypoint_scores', None)
            
            if pred_scores is not None:
                visibility = (pred_scores[i].cpu().numpy() > 0.3).flatten()
            else:
                visibility = None
            
            pred_image = draw_pose(pred_image, pred_keypoints, visibility)
        
        axes[0, i].imshow(pred_image)
        axes[0, i].set_title(f'Prediction {i}')
        axes[0, i].axis('off')
        
        # Bottom row: ground truth (if available)
        if ground_truth is not None:
            gt_image = image.copy()
            if 'keypoint_gt' in ground_truth:
                gt_keypoints = ground_truth['keypoint_gt'][i].cpu().numpy()
                gt_visibility = ground_truth.get('visibility', None)
                
                if gt_visibility is not None:
                    visibility = gt_visibility[i].cpu().numpy()
                else:
                    visibility = None
                
                gt_image = draw_pose(gt_image, gt_keypoints, visibility, color=(255, 0, 0))
            
            axes[1, i].imshow(gt_image)
            axes[1, i].set_title(f'Ground Truth {i}')
        else:
            axes[1, i].imshow(image)
            axes[1, i].set_title(f'Original {i}')
        
        axes[1, i].axis('off')
    
    plt.tight_layout()
    
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
    else:
        plt.show()
    
    return fig


def create_video_from_frames(frame_paths, output_path, fps=30):
    """
    Create video from frame images
    
    Args:
        frame_paths: List of frame image paths
        output_path: Output video path
        fps: Frames per second
    """
    if not frame_paths:
        return
    
    # Read first frame to get dimensions
    first_frame = cv2.imread(frame_paths[0])
    height, width, _ = first_frame.shape
    
    # Create video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    for frame_path in frame_paths:
        frame = cv2.imread(frame_path)
        out.write(frame)
    
    out.release()
    print(f"Video saved to: {output_path}")
