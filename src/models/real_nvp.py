"""
RealNVP implementation for RLE (Residual Log-likelihood Estimation)
Based on the original RLE paper implementation
"""

import torch
import torch.nn as nn
import torch.distributions as distributions


class RealNVP(nn.Module):
    """
    RealNVP (Real-valued Non-Volume Preserving) transformation for normalizing flows.
    Used in RLE to model the distribution of residuals.
    """
    
    def __init__(self, nets, nett, masks, prior):
        """
        Args:
            nets: List of scale networks
            nett: List of translation networks  
            masks: Binary masks for coupling layers
            prior: Prior distribution (typically standard Gaussian)
        """
        super(RealNVP, self).__init__()
        
        self.prior = prior
        self.masks = nn.Parameter(masks, requires_grad=False)
        self.t = torch.nn.ModuleList([nett() for _ in range(len(masks))])
        self.s = torch.nn.ModuleList([nets() for _ in range(len(masks))])
        
    def g(self, z):
        """Forward transformation: z -> x"""
        x = z
        for i in range(len(self.t)):
            x_ = x * self.masks[i]
            s = self.s[i](x_) * (1 - self.masks[i])
            t = self.t[i](x_) * (1 - self.masks[i])
            x = x_ + (1 - self.masks[i]) * (x * torch.exp(s) + t)
        return x

    def f(self, x):
        """Inverse transformation: x -> z"""
        log_det_J, z = x.new_zeros(x.shape[0]), x
        for i in reversed(range(len(self.t))):
            z_ = self.masks[i] * z
            s = self.s[i](z_) * (1 - self.masks[i])
            t = self.t[i](z_) * (1 - self.masks[i])
            z = (1 - self.masks[i]) * (z - t) * torch.exp(-s) + z_
            log_det_J -= s.sum(dim=1)
        return z, log_det_J

    def log_prob(self, x):
        """Compute log probability of x under the flow model"""
        z, log_det = self.f(x)
        log_prob = self.prior.log_prob(z) + log_det
        return log_prob

    def sample(self, batchSize):
        """Sample from the flow model"""
        z = self.prior.sample((batchSize,))
        x = self.g(z)
        return x


def nets():
    """Scale network for RealNVP"""
    return nn.Sequential(
        nn.Linear(2, 64), 
        nn.LeakyReLU(), 
        nn.Linear(64, 64), 
        nn.LeakyReLU(), 
        nn.Linear(64, 2), 
        nn.Tanh()
    )


def nett():
    """Translation network for RealNVP"""
    return nn.Sequential(
        nn.Linear(2, 64), 
        nn.LeakyReLU(), 
        nn.Linear(64, 64), 
        nn.LeakyReLU(), 
        nn.Linear(64, 2)
    )
