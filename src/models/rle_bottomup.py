"""
RLE-based Bottom-up Multi-Person Pose Estimation and Tracking Model

This model directly regresses:
1. Keypoint coordinates (x, y) + RLE sigma
2. Spatial connection vectors + RLE sigma  
3. Temporal connection vectors + RLE sigma

No explicit heatmaps or Re-ID features are used.
"""

import numpy as np
import torch
import torch.nn as nn
import torch.distributions as distributions
from easydict import EasyDict

from .backbone import ResNetBackbone
from .real_nvp import RealNVP, nets, nett


class Linear(nn.Module):
    """Custom linear layer with optional normalization"""
    
    def __init__(self, in_channel, out_channel, bias=True, norm=True):
        super(Linear, self).__init__()
        self.bias = bias
        self.norm = norm
        self.linear = nn.Linear(in_channel, out_channel, bias)
        nn.init.xavier_uniform_(self.linear.weight, gain=0.01)

    def forward(self, x):
        y = x.matmul(self.linear.weight.t())

        if self.norm:
            x_norm = torch.norm(x, dim=1, keepdim=True)
            y = y / x_norm

        if self.bias:
            y = y + self.linear.bias
        return y


class RLEBottomUpModel(nn.Module):
    """
    RLE-based Bottom-up Multi-Person Pose Estimation and Tracking Model
    
    Outputs:
    - Keypoint coordinates (x, y) for each joint type
    - Spatial connection vectors between joints of the same person
    - Temporal connection vectors between frames for tracking
    - RLE sigma values for uncertainty estimation of all above
    """
    
    def __init__(self, config):
        super(RLEBottomUpModel, self).__init__()
        
        self.config = config
        self.num_joints = config.NUM_JOINTS
        self.image_size = config.IMAGE_SIZE
        self.fc_dim = config.FC_DIM
        self.num_spatial_connections = len(config.SKELETON)  # Number of bone connections
        
        # Backbone network
        self.backbone = ResNetBackbone(
            num_layers=config.BACKBONE_LAYERS,
            pretrained=config.BACKBONE_PRETRAINED
        )
        
        # Global average pooling
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        
        # Fully connected layers
        self.fcs, out_channel = self._make_fc_layer()
        
        # Output heads for different predictions
        # 1. Keypoint coordinates (x, y) for each joint
        self.fc_keypoint_coord = Linear(out_channel, self.num_joints * 2)
        self.fc_keypoint_sigma = Linear(out_channel, self.num_joints * 2, norm=False)
        
        # 2. Spatial connection vectors (dx, dy) for each bone connection
        self.fc_spatial_coord = Linear(out_channel, self.num_spatial_connections * 2)
        self.fc_spatial_sigma = Linear(out_channel, self.num_spatial_connections * 2, norm=False)
        
        # 3. Temporal connection vectors (dx, dy) for tracking
        # We predict one temporal vector per joint for simplicity
        self.fc_temporal_coord = Linear(out_channel, self.num_joints * 2)
        self.fc_temporal_sigma = Linear(out_channel, self.num_joints * 2, norm=False)
        
        # RLE Flow models for each prediction type
        prior = distributions.MultivariateNormal(torch.zeros(2), torch.eye(2))
        masks = torch.from_numpy(np.array([[0, 1], [1, 0]] * 3).astype(np.float32))
        
        self.flow_keypoint = RealNVP(nets, nett, masks, prior)
        self.flow_spatial = RealNVP(nets, nett, masks, prior)
        self.flow_temporal = RealNVP(nets, nett, masks, prior)
        
        # Store skeleton connections for spatial connection processing
        self.skeleton = config.SKELETON
        
    def _make_fc_layer(self):
        """Create fully connected layers"""
        fc_layers = []
        input_channel = self.backbone.feature_channels
        
        for dim in self.fc_dim:
            if dim > 0:
                fc = nn.Linear(input_channel, dim)
                bn = nn.BatchNorm1d(dim)
                fc_layers.append(fc)
                fc_layers.append(bn)
                fc_layers.append(nn.ReLU(inplace=True))
                input_channel = dim
            else:
                fc_layers.append(nn.Identity())
        
        return nn.Sequential(*fc_layers), input_channel
    
    def forward(self, x, labels=None):
        """
        Forward pass
        
        Args:
            x: Input images (B, 3, H, W)
            labels: Ground truth labels for training (optional)
            
        Returns:
            Dictionary containing predictions and losses
        """
        batch_size = x.shape[0]
        
        # Extract features using backbone
        feat = self.backbone(x)
        
        # Global average pooling
        feat = self.avg_pool(feat).reshape(batch_size, -1)
        
        # Pass through fully connected layers
        feat = self.fcs(feat)
        
        # Predict keypoint coordinates and uncertainties
        keypoint_coord = self.fc_keypoint_coord(feat).reshape(batch_size, self.num_joints, 2)
        keypoint_sigma = self.fc_keypoint_sigma(feat).reshape(batch_size, self.num_joints, 2).sigmoid()
        
        # Predict spatial connection vectors and uncertainties
        spatial_coord = self.fc_spatial_coord(feat).reshape(batch_size, self.num_spatial_connections, 2)
        spatial_sigma = self.fc_spatial_sigma(feat).reshape(batch_size, self.num_spatial_connections, 2).sigmoid()
        
        # Predict temporal connection vectors and uncertainties
        temporal_coord = self.fc_temporal_coord(feat).reshape(batch_size, self.num_joints, 2)
        temporal_sigma = self.fc_temporal_sigma(feat).reshape(batch_size, self.num_joints, 2).sigmoid()
        
        # Compute confidence scores (1 - sigma)
        keypoint_scores = 1 - torch.mean(keypoint_sigma, dim=2, keepdim=True)
        spatial_scores = 1 - torch.mean(spatial_sigma, dim=2, keepdim=True)
        temporal_scores = 1 - torch.mean(temporal_sigma, dim=2, keepdim=True)
        
        # Compute RLE losses during training
        losses = {}
        if self.training and labels is not None:
            losses = self._compute_rle_losses(
                keypoint_coord, keypoint_sigma,
                spatial_coord, spatial_sigma,
                temporal_coord, temporal_sigma,
                labels, batch_size
            )
        
        output = EasyDict(
            # Keypoint predictions
            keypoint_coord=keypoint_coord,
            keypoint_sigma=keypoint_sigma,
            keypoint_scores=keypoint_scores,
            
            # Spatial connection predictions
            spatial_coord=spatial_coord,
            spatial_sigma=spatial_sigma,
            spatial_scores=spatial_scores,
            
            # Temporal connection predictions
            temporal_coord=temporal_coord,
            temporal_sigma=temporal_sigma,
            temporal_scores=temporal_scores,
            
            # Losses
            **losses
        )
        
        return output

    def _compute_rle_losses(self, keypoint_coord, keypoint_sigma, spatial_coord, spatial_sigma,
                           temporal_coord, temporal_sigma, labels, batch_size):
        """
        Compute RLE losses for all prediction types

        Args:
            keypoint_coord: Predicted keypoint coordinates (B, J, 2)
            keypoint_sigma: Predicted keypoint uncertainties (B, J, 2)
            spatial_coord: Predicted spatial connection vectors (B, S, 2)
            spatial_sigma: Predicted spatial uncertainties (B, S, 2)
            temporal_coord: Predicted temporal connection vectors (B, J, 2)
            temporal_sigma: Predicted temporal uncertainties (B, J, 2)
            labels: Ground truth labels
            batch_size: Batch size

        Returns:
            Dictionary of losses
        """
        losses = {}

        # 1. Keypoint RLE loss
        if 'keypoint_gt' in labels:
            gt_keypoints = labels['keypoint_gt'].reshape(keypoint_coord.shape)
            keypoint_residual = (keypoint_coord - gt_keypoints) / keypoint_sigma

            log_phi_keypoint = self.flow_keypoint.log_prob(
                keypoint_residual.reshape(-1, 2)
            ).reshape(batch_size, self.num_joints, 1)

            keypoint_loss = torch.log(keypoint_sigma) - log_phi_keypoint.expand(-1, -1, 2)
            losses['keypoint_loss'] = keypoint_loss.mean()

        # 2. Spatial connection RLE loss
        if 'spatial_gt' in labels:
            gt_spatial = labels['spatial_gt'].reshape(spatial_coord.shape)
            spatial_residual = (spatial_coord - gt_spatial) / spatial_sigma

            log_phi_spatial = self.flow_spatial.log_prob(
                spatial_residual.reshape(-1, 2)
            ).reshape(batch_size, self.num_spatial_connections, 1)

            spatial_loss = torch.log(spatial_sigma) - log_phi_spatial.expand(-1, -1, 2)
            losses['spatial_loss'] = spatial_loss.mean()

        # 3. Temporal connection RLE loss
        if 'temporal_gt' in labels:
            gt_temporal = labels['temporal_gt'].reshape(temporal_coord.shape)
            temporal_residual = (temporal_coord - gt_temporal) / temporal_sigma

            log_phi_temporal = self.flow_temporal.log_prob(
                temporal_residual.reshape(-1, 2)
            ).reshape(batch_size, self.num_joints, 1)

            temporal_loss = torch.log(temporal_sigma) - log_phi_temporal.expand(-1, -1, 2)
            losses['temporal_loss'] = temporal_loss.mean()

        # Total loss
        total_loss = 0
        if 'keypoint_loss' in losses:
            total_loss += losses['keypoint_loss']
        if 'spatial_loss' in losses:
            total_loss += losses['spatial_loss']
        if 'temporal_loss' in losses:
            total_loss += losses['temporal_loss']

        losses['total_loss'] = total_loss

        return losses
